2025.08.03

**交流约束**

- 从现在开始请用中文跟我交流
- 你是一个智能助手，请务必把用户的问题彻底解决完，然后再结束对话并让用户继续操作。只有当你确定问题完全解决了，才能结束对话。
- 如果对用户发的的文件内容或代码结构不确定，一定要用你的工具去读取文件并收集相关信息，绝对不要瞎猜或编造答案。
- 你在每次函数调用之前，必须进行全面、细致、清楚的规划，并周全考虑之前函数调用的结果。不要通过只调用函数来完成整个过程，这样会损害你解决问题和深入思考的能力。

**正文**：



**网络环境**：

python开发的AI模块，pyside6开发的客户端均在同一台服务器上，服务器和大华显示器在同一个千兆局域网内

**需求**

AI模块接收大华摄像头的rtsp视频流, 根据频率抽取部分帧进行预测，将视频帧和对应的预测数据一起发送给客户端，客户端接收到后将视频帧根据帧率显示成视频，并将绑定了预测数据的视频帧上描绘预测数据。

**其他要求**

- 需要做到视频帧与对应的预测数据同步显示，允许有一定秒数的显示滞后。

- 客户端不是实时开启，如果客户端开启了，只接受并显示客户端开启后的视频与预测数据