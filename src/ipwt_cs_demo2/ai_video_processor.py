import time
import threading
import random
import cv2
import zmq
import msgpack
import numpy as np
from collections import deque
from typing import Optional, List, Dict, Any

class AIVideoProcessor:
    def __init__(self, rtsp_url: str = None, zmq_port: int = 5555):
        self.rtsp_url = rtsp_url or "rtsp://client:@edc12345@10.10.10.61:554/cam/realmonitor?channel=1&subtype=0"
        self.zmq_port = zmq_port
        
        # 序列号和缓冲区
        self.frame_sequence = 0
        self.frame_buffer = deque(maxlen=10)  # 待处理帧缓冲
        self.send_queue = deque(maxlen=30)    # 待发送队列
        
        # ZeroMQ设置
        self.context = zmq.Context()
        self.socket = self.context.socket(zmq.PUB)
        self.socket.setsockopt(zmq.CONFLATE, 1)  # 只保留最新消息
        self.socket.bind(f"tcp://*:{zmq_port}")
        
        # 线程控制
        self.running = True
        self.threads = []
        
        # 性能统计
        self.prediction_times = deque(maxlen=10)
        
        print(f"AI模块初始化完成，ZeroMQ端口: {zmq_port}")
    
    def simulate_ai_prediction(self, frame: np.ndarray) -> List[Dict[str, Any]]:
        """模拟AI预测，耗时1-2秒，返回3个随机矩形框"""
        # 模拟预测耗时
        prediction_time = random.uniform(1.0, 2.0)
        time.sleep(prediction_time)
        
        # 记录预测时间
        self.prediction_times.append(prediction_time)
        
        # 获取图像尺寸
        height, width = frame.shape[:2]
        
        # 生成3个随机矩形框
        predictions = []
        for i in range(3):
            # 随机生成矩形框坐标
            x1 = random.randint(0, width - 200)
            y1 = random.randint(0, height - 150)
            x2 = x1 + random.randint(100, 200)
            y2 = y1 + random.randint(80, 150)
            
            # 确保坐标在图像范围内
            x2 = min(x2, width)
            y2 = min(y2, height)
            
            prediction = {
                'id': i + 1,
                'class': random.choice(['person', 'car', 'bicycle', 'dog', 'cat']),
                'confidence': round(random.uniform(0.6, 0.95), 2),
                'bbox': [x1, y1, x2, y2],  # [x1, y1, x2, y2]
                'color': [
                    random.randint(0, 255),
                    random.randint(0, 255), 
                    random.randint(0, 255)
                ]
            }
            predictions.append(prediction)
        
        avg_time = sum(self.prediction_times) / len(self.prediction_times)
        print(f"AI预测完成，耗时: {prediction_time:.2f}s, 平均耗时: {avg_time:.2f}s")
        
        return predictions
    
    def video_capture_thread(self):
        """视频捕获线程"""
        print("启动视频捕获线程...")
        
        # 尝试连接RTSP流，失败则使用摄像头
        cap = cv2.VideoCapture(self.rtsp_url)
        if not cap.isOpened():
            print(f"无法连接RTSP: {self.rtsp_url}，尝试使用本地摄像头...")
            cap = cv2.VideoCapture(0)
            
        if not cap.isOpened():
            print("无法打开视频源，使用模拟视频...")
            cap = None
        
        frame_count = 0
        while self.running:
            if cap is not None:
                ret, frame = cap.read()
                if not ret:
                    print("视频流中断，重新连接...")
                    cap.release()
                    time.sleep(1)
                    cap = cv2.VideoCapture(self.rtsp_url)
                    continue
            else:
                # 生成模拟视频帧
                frame = self.generate_mock_frame(frame_count)
            
            self.frame_sequence += 1
            frame_count += 1
            
            frame_data = {
                'sequence': self.frame_sequence,
                'frame': frame,
                'timestamp': time.time(),
                'predictions': None,
                'has_predictions': False
            }
            
            # 添加到缓冲区
            if len(self.frame_buffer) < self.frame_buffer.maxlen:
                self.frame_buffer.append(frame_data)
            else:
                print("帧缓冲区满，丢弃最旧帧")
                self.frame_buffer.popleft()
                self.frame_buffer.append(frame_data)
            
            time.sleep(1/30)  # 30fps采集
        
        if cap:
            cap.release()
        print("视频捕获线程结束")
    
    def generate_mock_frame(self, frame_count: int) -> np.ndarray:
        """生成模拟视频帧"""
        # 创建640x480的彩色图像
        frame = np.zeros((480, 640, 3), dtype=np.uint8)
        
        # 添加渐变背景
        for i in range(480):
            frame[i, :] = [i//2, (frame_count + i) % 255, 255 - i//2]
        
        # 添加移动的圆形
        center_x = int(320 + 200 * np.sin(frame_count * 0.05))
        center_y = int(240 + 100 * np.cos(frame_count * 0.03))
        cv2.circle(frame, (center_x, center_y), 30, (255, 255, 255), -1)
        
        # 添加帧计数文本
        cv2.putText(frame, f"Frame: {frame_count}", (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        return frame
    
    def ai_prediction_thread(self):
        """AI预测线程"""
        print("启动AI预测线程...")
        
        while self.running:
            if self.frame_buffer:
                # 取出最早的帧进行预测
                frame_data = self.frame_buffer.popleft()
                
                print(f"开始预测帧 {frame_data['sequence']}...")
                
                # AI预测（耗时操作）
                predictions = self.simulate_ai_prediction(frame_data['frame'])
                
                # 将预测结果绑定到对应帧
                frame_data['predictions'] = predictions
                frame_data['has_predictions'] = True
                
                # 加入发送队列
                self.send_queue.append(frame_data)
                
                # 将缓冲区中的其他帧（无预测数据）也加入发送队列
                while self.frame_buffer:
                    next_frame = self.frame_buffer.popleft()
                    self.send_queue.append(next_frame)
                    
                print(f"帧 {frame_data['sequence']} 预测完成，发送队列长度: {len(self.send_queue)}")
            else:
                time.sleep(0.01)  # 等待新帧
        
        print("AI预测线程结束")
    
    def zmq_sender_thread(self):
        """ZeroMQ发送线程"""
        print("启动ZeroMQ发送线程...")
        
        while self.running:
            if self.send_queue:
                frame_data = self.send_queue.popleft()
                
                # 编码图像为JPEG
                _, encoded_frame = cv2.imencode('.jpg', frame_data['frame'], 
                                              [cv2.IMWRITE_JPEG_QUALITY, 80])
                
                message = {
                    'sequence': frame_data['sequence'],
                    'frame': encoded_frame.tobytes(),
                    'predictions': frame_data['predictions'],
                    'has_predictions': frame_data['has_predictions'],
                    'timestamp': frame_data['timestamp']
                }
                
                try:
                    self.socket.send(msgpack.packb(message), zmq.NOBLOCK)
                    
                    if frame_data['has_predictions']:
                        print(f"发送帧 {frame_data['sequence']} (含预测数据)")
                    else:
                        print(f"发送帧 {frame_data['sequence']} (无预测数据)")
                        
                except zmq.Again:
                    print("ZeroMQ发送缓冲区满，丢弃帧")
                    
            else:
                time.sleep(0.001)  # 高频检查发送队列
        
        print("ZeroMQ发送线程结束")
    
    def start(self):
        """启动所有线程"""
        print("启动AI视频处理器...")
        
        # 创建并启动线程
        threads_config = [
            ('video_capture', self.video_capture_thread),
            ('ai_prediction', self.ai_prediction_thread),
            ('zmq_sender', self.zmq_sender_thread)
        ]
        
        for name, target in threads_config:
            thread = threading.Thread(target=target, name=name, daemon=True)
            thread.start()
            self.threads.append(thread)
            print(f"线程 {name} 已启动")
        
        print("所有线程启动完成")
    
    def stop(self):
        """停止所有线程"""
        print("停止AI视频处理器...")
        self.running = False
        
        # 等待线程结束
        for thread in self.threads:
            thread.join(timeout=2)
        
        # 清理资源
        self.socket.close()
        self.context.term()
        print("AI视频处理器已停止")
    
    def get_status(self) -> Dict[str, Any]:
        """获取运行状态"""
        avg_prediction_time = 0
        if self.prediction_times:
            avg_prediction_time = sum(self.prediction_times) / len(self.prediction_times)
        
        return {
            'running': self.running,
            'current_sequence': self.frame_sequence,
            'frame_buffer_size': len(self.frame_buffer),
            'send_queue_size': len(self.send_queue),
            'avg_prediction_time': round(avg_prediction_time, 2),
            'active_threads': len([t for t in self.threads if t.is_alive()])
        }

def main():
    # 创建AI处理器
    processor = AIVideoProcessor(
        rtsp_url="rtsp://client:@<EMAIL>:33611/cam/realmonitor?channel=1&subtype=0",
        zmq_port=5555
    )
    
    try:
        # 启动处理器
        processor.start()
        
        # 主循环 - 显示状态信息
        while True:
            time.sleep(5)
            status = processor.get_status()
            print(f"\n=== 状态信息 ===")
            print(f"运行状态: {status['running']}")
            print(f"当前序列号: {status['current_sequence']}")
            print(f"帧缓冲区: {status['frame_buffer_size']}")
            print(f"发送队列: {status['send_queue_size']}")
            print(f"平均预测时间: {status['avg_prediction_time']}s")
            print(f"活跃线程数: {status['active_threads']}")
            
    except KeyboardInterrupt:
        print("\n收到停止信号...")
    finally:
        processor.stop()

if __name__ == "__main__":
    main()