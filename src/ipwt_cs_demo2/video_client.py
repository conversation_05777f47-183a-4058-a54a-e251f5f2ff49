import time
import threading
import cv2
import zmq
import msgpack
import numpy as np
from collections import deque
from typing import Optional, List, Dict, Any

class VideoClient:
    def __init__(self, zmq_host: str = "localhost", zmq_port: int = 5555):
        self.zmq_host = zmq_host
        self.zmq_port = zmq_port
        
        # 接收缓冲区和状态
        self.frame_buffer = deque(maxlen=90)  # 3秒缓冲
        self.current_predictions = None  # 当前显示的预测数据
        self.expected_sequence = 1
        
        # ZeroMQ设置
        self.context = zmq.Context()
        self.socket = self.context.socket(zmq.SUB)
        self.socket.setsockopt(zmq.SUBSCRIBE, b"")  # 订阅所有消息
        self.socket.setsockopt(zmq.CONFLATE, 1)     # 只保留最新消息
        self.socket.connect(f"tcp://{zmq_host}:{zmq_port}")
        
        # 线程控制
        self.running = True
        self.threads = []
        
        # 性能统计
        self.frames_received = 0
        self.frames_displayed = 0
        self.predictions_updated = 0
        
        print(f"客户端初始化完成，连接到 {zmq_host}:{zmq_port}")
    
    def zmq_receiver_thread(self):
        """ZeroMQ接收线程"""
        print("启动ZeroMQ接收线程...")
        
        while self.running:
            try:
                # 非阻塞接收
                data = msgpack.unpackb(self.socket.recv(zmq.NOBLOCK))
                self.frames_received += 1
                
                # 添加到缓冲区
                if len(self.frame_buffer) < self.frame_buffer.maxlen:
                    self.frame_buffer.append(data)
                else:
                    # 缓冲区满，移除最旧的帧
                    self.frame_buffer.popleft()
                    self.frame_buffer.append(data)
                
                if data['has_predictions']:
                    print(f"接收到帧 {data['sequence']} (含预测数据)")
                
            except zmq.Again:
                time.sleep(0.001)  # 无数据时短暂等待
            except Exception as e:
                print(f"接收数据错误: {e}")
                time.sleep(0.1)
        
        print("ZeroMQ接收线程结束")
    
    def display_thread(self):
        """显示线程 - 按序播放"""
        print("启动显示线程...")
        
        while self.running:
            # 查找下一个应该显示的帧
            next_frame = self.get_next_sequential_frame()
            
            if next_frame:
                # 解码图像
                frame_bytes = np.frombuffer(next_frame['frame'], dtype=np.uint8)
                frame = cv2.imdecode(frame_bytes, cv2.IMREAD_COLOR)
                
                if frame is not None:
                    # 检查是否有新的预测数据
                    if next_frame['has_predictions']:
                        self.current_predictions = next_frame['predictions']
                        self.predictions_updated += 1
                        print(f"更新预测数据: 序列号 {next_frame['sequence']}, "
                              f"检测到 {len(self.current_predictions)} 个对象")
                    
                    # 显示帧 + 当前有效的预测数据
                    display_frame = self.draw_predictions_on_frame(frame, self.current_predictions)
                    
                    # 添加状态信息
                    self.draw_status_info(display_frame, next_frame['sequence'])
                    
                    cv2.imshow('AI Video Stream', display_frame)
                    
                    self.frames_displayed += 1
                    self.expected_sequence += 1
                    
                    # 检查退出键
                    if cv2.waitKey(1) & 0xFF == ord('q'):
                        self.running = False
                        break
                else:
                    print(f"无法解码帧 {next_frame['sequence']}")
            
            time.sleep(1/30)  # 30fps显示
        
        cv2.destroyAllWindows()
        print("显示线程结束")
    
    def get_next_sequential_frame(self) -> Optional[Dict[str, Any]]:
        """获取下一个顺序帧"""
        # 查找期望的序列号
        for frame_data in list(self.frame_buffer):
            if frame_data['sequence'] == self.expected_sequence:
                self.frame_buffer.remove(frame_data)
                return frame_data
        
        # 如果找不到期望的序列号，检查是否有跳跃
        if self.frame_buffer:
            sequences = [f['sequence'] for f in self.frame_buffer]
            min_seq = min(sequences)
            max_seq = max(sequences)
            
            # 如果最小序列号大于期望序列号，说明有帧丢失
            if min_seq > self.expected_sequence:
                gap = min_seq - self.expected_sequence
                print(f"检测到帧跳跃: 期望 {self.expected_sequence}, 最小 {min_seq}, 跳跃 {gap} 帧")
                self.expected_sequence = min_seq  # 跳跃到最小序列号
                return self.get_next_sequential_frame()
            
            # 如果缓冲区积压过多，跳跃到较新的帧
            if len(self.frame_buffer) > 60:  # 2秒积压
                target_seq = max_seq - 30  # 保留1秒缓冲
                print(f"缓冲区积压过多，跳跃到序列号 {target_seq}")
                self.expected_sequence = target_seq
                return self.get_next_sequential_frame()
        
        return None
    
    def draw_predictions_on_frame(self, frame: np.ndarray, 
                                predictions: Optional[List[Dict[str, Any]]]) -> np.ndarray:
        """在帧上绘制预测数据"""
        display_frame = frame.copy()
        
        if predictions:
            for pred in predictions:
                # 获取边界框坐标
                x1, y1, x2, y2 = pred['bbox']
                color = tuple(pred['color'])
                
                # 绘制矩形框
                cv2.rectangle(display_frame, (x1, y1), (x2, y2), color, 2)
                
                # 绘制标签
                label = f"{pred['class']} {pred['confidence']:.2f}"
                label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
                
                # 标签背景
                cv2.rectangle(display_frame, 
                            (x1, y1 - label_size[1] - 10), 
                            (x1 + label_size[0], y1), 
                            color, -1)
                
                # 标签文字
                cv2.putText(display_frame, label, (x1, y1 - 5), 
                          cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        
        return display_frame
    
    def draw_status_info(self, frame: np.ndarray, current_sequence: int):
        """绘制状态信息"""
        height, width = frame.shape[:2]
        
        # 状态信息
        status_lines = [
            f"Sequence: {current_sequence}",
            f"Buffer: {len(self.frame_buffer)}",
            f"Received: {self.frames_received}",
            f"Displayed: {self.frames_displayed}",
            f"Predictions: {self.predictions_updated}",
            f"Objects: {len(self.current_predictions) if self.current_predictions else 0}"
        ]
        
        # 绘制半透明背景
        overlay = frame.copy()
        cv2.rectangle(overlay, (10, 10), (300, 200), (0, 0, 0), -1)
        cv2.addWeighted(overlay, 0.7, frame, 0.3, 0, frame)
        
        # 绘制状态文字
        for i, line in enumerate(status_lines):
            y = 35 + i * 25
            cv2.putText(frame, line, (20, y), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
    
    def start(self):
        """启动客户端"""
        print("启动视频客户端...")
        
        # 创建并启动线程
        threads_config = [
            ('zmq_receiver', self.zmq_receiver_thread),
            ('display', self.display_thread)
        ]
        
        for name, target in threads_config:
            thread = threading.Thread(target=target, name=name, daemon=True)
            thread.start()
            self.threads.append(thread)
            print(f"线程 {name} 已启动")
        
        print("客户端启动完成，按 'q' 键退出")
    
    def stop(self):
        """停止客户端"""
        print("停止视频客户端...")
        self.running = False
        
        # 等待线程结束
        for thread in self.threads:
            thread.join(timeout=2)
        
        # 清理资源
        self.socket.close()
        self.context.term()
        cv2.destroyAllWindows()
        print("视频客户端已停止")
    
    def get_status(self) -> Dict[str, Any]:
        """获取运行状态"""
        return {
            'running': self.running,
            'expected_sequence': self.expected_sequence,
            'buffer_size': len(self.frame_buffer),
            'frames_received': self.frames_received,
            'frames_displayed': self.frames_displayed,
            'predictions_updated': self.predictions_updated,
            'current_objects': len(self.current_predictions) if self.current_predictions else 0
        }

def main():
    # 创建客户端
    client = VideoClient(zmq_host="localhost", zmq_port=5555)
    
    try:
        # 启动客户端
        client.start()
        
        # 主循环 - 等待用户退出
        while client.running:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n收到停止信号...")
    finally:
        client.stop()

if __name__ == "__main__":
    main()