import subprocess
import sys
import time

def install_requirements():
    """安装依赖包"""
    requirements = [
        "opencv-python",
        "pyzmq", 
        "msgpack",
        "numpy"
    ]
    
    for package in requirements:
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"✓ {package} 安装成功")
        except subprocess.CalledProcessError:
            print(f"✗ {package} 安装失败")

def main():
    print("=== AI视频流处理Demo ===")
    print("1. 安装依赖包")
    print("2. 启动AI模块")
    print("3. 启动客户端")
    print("4. 同时启动")
    
    choice = input("请选择操作 (1-4): ").strip()
    
    if choice == "1":
        install_requirements()
    elif choice == "2":
        subprocess.run([sys.executable, "ai_video_processor.py"])
    elif choice == "3":
        subprocess.run([sys.executable, "video_client.py"])
    elif choice == "4":
        print("启动AI模块...")
        ai_process = subprocess.Popen([sys.executable, "ai_video_processor.py"])
        
        print("等待3秒后启动客户端...")
        time.sleep(3)
        
        try:
            subprocess.run([sys.executable, "video_client.py"])
        finally:
            print("终止AI模块...")
            ai_process.terminate()
    else:
        print("无效选择")

if __name__ == "__main__":
    main()